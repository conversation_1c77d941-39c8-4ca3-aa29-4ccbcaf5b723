<template>
  <div class="login-container">
    <div class="login-weaper animated bounceInDown">
      <div class="login-logo">
        <img src="/img/logo.png" alt=""/>
      </div>
      <p class="login-tip">{{ website.title }} v 4.5</p>
      <div class="login-border">
        <div class="login-main">
          <h4 class="login-title" v-if="!website.domainAutoTenant">
            <el-select
              v-model="active"
              class="login-select animated fadeIn"
              placeholder="点击请选择做市商"
              @change="handleCommand"
            >
              <el-option
                v-for="tenant in tenantList"
                :key="tenant.id"
                :label="tenant.name"
                :value="tenant.id"
              ></el-option>
            </el-select>
          </h4>
          <userLogin v-if="activeName === 'user'"/>
        </div>
      </div>
    </div>
    <div class="login-copyright">
      {{ website.copyright }}
    </div>
    <top-color v-show="false"/>
  </div>
</template>
<script>
import {fetchList} from "@/api/admin/tenant";
import userLogin from "./userlogin";
import {mapGetters} from "vuex";
import {getStore, setStore} from "@/util/store";
import topColor from "@/page/index/top/top-color";
import website from "@/const/website";

export default {
  name: "Login",
  components: {
    userLogin,
    topColor,
  },
  data() {
    return {
      active: "",
      tenantList: [],
      activeName: "user",
      socialForm: {},
    };
  },
  watch: {
    $route: {
      handler() {
        const params = this.$route.query;
        if (this.validatenull(params.state) && this.validatenull(params.code))
          return;

        this.socialForm.state = params.state;
        this.socialForm.code = params.code;

        const loading = this.$loading({
          lock: true,
          text: `登录中,请稍后。。。`,
          spinner: "el-icon-loading",
        });

        this.$store
          .dispatch("LoginBySocial", this.socialForm)
          .then(() => {
            loading.close();
            this.$router.push({path: this.tagWel.value});
          })
          .catch(() => {
            loading.close();
          });
      },
      immediate: true,
    },
  },
  created() {
    this.active = getStore({name: "tenantId"});

    this.getTenantList().then(() => {
      // 开启自动做市商填充
      if (website.domainAutoTenant) {
        this.handleAutoTenant()
      }
    })
  },
  computed: {
    ...mapGetters(["website", "tagWel"]),
  },
  methods: {
    handleCommand(command) {
      // 写入到 sessionStore 方便 axios 每次请求获取 tab 范围内的做市商ID
      setStore({name: "tenantId", content: command, type: "session"});
      // 写入到 localStore 方便 下次登录回显上次选择的做市商ID
      setStore({name: "tenantId", content: command});
    },
    getTenantList() {
      return fetchList().then((response) => {
        this.tenantList = response.data.data;
      })
    },
    handleAutoTenant() {
      let domain = window.location.host
      this.tenantList.forEach(tenant => {
        if (domain.indexOf(tenant.tenantDomain) >= 0) {
          this.handleCommand(tenant.id)
          console.log("当前域名:", domain, "自动匹配做市商:", tenant.name)
        }
      })
    }
  },
};
</script>

<style lang="scss">
@import "@/styles/login.scss";
</style>
