<template>
    <div class="workbench">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-left">
                <h1 class="page-title">数据看板</h1>
                <div class="page-subtitle">实时监控业务数据，快速响应市场变化</div>
            </div>
            <div class="page-actions">
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    style="width: 240px; margin-right: 12px;"
                    :shortcuts="dateShortcuts"
                />
                <el-button type="primary" @click="refreshData" size="small">刷新</el-button>
                <el-dropdown @command="handleExport" trigger="click">
                    <el-button size="small">
                        导出
                        <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="all">导出全部数据</el-dropdown-item>
                            <el-dropdown-item command="chart">导出图表数据</el-dropdown-item>
                            <el-dropdown-item command="table">导出表格数据</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>

        <!-- 数据概览卡片 -->
        <div class="overview-cards">
            <el-row :gutter="16" align="stretch" style="display: flex; flex-wrap: nowrap; overflow-x: auto;">
                <el-col
                    v-for="(card, idx) in allCards"
                    :key="card['isRisk'] ? 'risk-threshold' : card.title"
                    style="flex: 1; min-width: 180px; margin-bottom: 12px;"
                >
                    <el-card class="overview-card" shadow="hover">
                        <div class="card-content">
                            <template v-if="card['isRisk']">
                                <div class="card-icon" style="background-color: #FF9800;">
                                    <el-icon :size="20" color="#fff">
                                        <Warning />
                                    </el-icon>
                                </div>
                                <div class="card-info">
                                    <div class="card-title">平台风控阈值</div>
                                    <div class="card-value">¥{{ riskThreshold.toLocaleString() }}</div>
                                    <el-button size="small" type="primary" @click="showRiskThresholdDialog" style="margin-top: 4px;">
                                        设置阈值
                                    </el-button>
                                </div>
                            </template>
                            <template v-else>
                                <div class="card-icon" :style="{ backgroundColor: card.color }">
                                    <el-icon :size="20" :color="'#fff'">
                                        <component :is="card.icon" />
                                    </el-icon>
                                </div>
                                <div class="card-info">
                                    <div class="card-title">{{ card.title }}</div>
                                    <div class="card-value">{{ card.value }}</div>
                                    <div class="card-trend" :class="card.trend > 0 ? 'up' : 'down'">
                                        <el-icon size="12">
                                            <component :is="card.trend > 0 ? 'ArrowUp' : 'ArrowDown'" />
                                        </el-icon>
                                        {{ Math.abs(card.trend) }}%
                                    </div>
                                    <div class="card-detail" v-if="card.detail">{{ card.detail }}</div>
                                </div>
                            </template>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 主要图表区域 -->
        <div class="main-charts">
            <el-row :gutter="16">
                <!-- 交易趋势图 -->
                <el-col :span="16">
                    <el-card class="chart-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">交易趋势分析</span>
                                <el-radio-group v-model="trendTimeRange" size="small">
                                    <el-radio-button label="7d">7天</el-radio-button>
                                    <el-radio-button label="30d">30天</el-radio-button>
                                    <el-radio-button label="90d">90天</el-radio-button>
                                </el-radio-group>
                            </div>
                        </template>
                        <div class="chart-container">
                            <v-chart class="chart" :option="trendChartOption || defaultChartOption" @error="handleChartError" v-loading="chartLoading" />
                        </div>
                    </el-card>
                </el-col>

                <!-- 交易类型分布 -->
                <el-col :span="8">
                    <el-card class="chart-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">交易类型分布</span>
                            </div>
                        </template>
                        <div class="chart-container">
                            <v-chart class="chart" :option="pieChartOption || defaultChartOption" @error="handleChartError" v-loading="chartLoading" />
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 次要图表区域 -->
        <div class="secondary-charts">
            <el-row :gutter="16">
                <!-- 用户活跃度分析 -->
                <el-col :span="12">
                    <el-card class="chart-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">用户活跃度</span>
                                <el-select v-model="userActivityType" size="small" style="width: 100px;">
                                    <el-option label="日活跃" value="daily" />
                                    <el-option label="周活跃" value="weekly" />
                                    <el-option label="月活跃" value="monthly" />
                                </el-select>
                            </div>
                        </template>
                        <div class="chart-container">
                            <v-chart class="chart" :option="userActivityOption || defaultChartOption" @error="handleChartError" v-loading="chartLoading" />
                        </div>
                    </el-card>
                </el-col>

                <!-- 收入分析 -->
                <el-col :span="12">
                    <el-card class="chart-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">收入分析</span>
                                <el-radio-group v-model="incomeType" size="small">
                                    <el-radio-button label="daily">日</el-radio-button>
                                    <el-radio-button label="monthly">月</el-radio-button>
                                </el-radio-group>
                            </div>
                        </template>
                        <div class="chart-container">
                            <v-chart class="chart" :option="incomeChartOption || defaultChartOption" @error="handleChartError" v-loading="chartLoading" />
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 监控和操作区域 -->
        <div class="monitor-section">
            <el-row :gutter="16">
                <!-- 风险监控 -->
                <el-col :span="8">
                    <el-card class="monitor-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">风险监控</span>
                                <el-tag type="warning" size="small">实时</el-tag>
                            </div>
                        </template>
                        <div class="risk-indicators">
                            <div class="risk-item" v-for="item in riskIndicators" :key="item.name">
                                <div class="risk-info">
                                    <span class="risk-name">{{ item.name }}</span>
                                    <span class="risk-value" :class="item.level">{{ item.value }}</span>
                                </div>
                                <el-progress
                                    :percentage="item.percentage"
                                    :color="item.color"
                                    :stroke-width="6"
                                    :show-text="false"
                                />
                            </div>
                        </div>
                    </el-card>
                </el-col>

                <!-- 实时通知 -->
                <el-col :span="8">
                    <el-card class="notification-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">系统通知</span>
                                <el-badge :value="unreadNotifications.length" :hidden="unreadNotifications.length === 0">
                                    <el-button type="text" size="small" @click="markAllAsRead">
                                        全部已读
                                    </el-button>
                                </el-badge>
                            </div>
                        </template>
                        <div class="notification-list">
                            <div 
                                v-for="notification in notifications.slice(0, 4)" 
                                :key="notification.id"
                                class="notification-item"
                                :class="{ unread: !notification.read }"
                                @click="markAsRead(notification)"
                            >
                                <div class="notification-icon" :class="notification.type">
                                    <el-icon size="14">
                                        <component :is="getNotificationIcon(notification.type)" />
                                    </el-icon>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">{{ notification.title }}</div>
                                    <div class="notification-time">{{ notification.time }}</div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </el-col>

                <!-- 快捷操作 -->
                <el-col :span="8">
                    <el-card class="quick-actions-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">快捷操作</span>
                            </div>
                        </template>
                        <div class="quick-actions">
                            <div class="action-grid">
                                <div 
                                    v-for="action in quickActions.slice(0, 6)" 
                                    :key="action.name"
                                    class="action-item"
                                    @click="executeQuickAction(action)"
                                >
                                    <div class="action-icon" :style="{ backgroundColor: action.color }">
                                        <el-icon :size="16" color="#fff">
                                            <component :is="action.icon" />
                                        </el-icon>
                                    </div>
                                    <div class="action-name">{{ action.name }}</div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 交易监控区域 -->
        <div class="transaction-monitor-section">
            <el-row :gutter="16">
                <!-- 异常交易监控 -->
                <el-col :span="12">
                    <el-card class="abnormal-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">异常交易监控</span>
                                <div class="header-actions">
                                    <el-button type="danger" size="small" @click="handleRiskAlert">
                                        处理异常
                                    </el-button>
                                </div>
                            </div>
                        </template>
                        <el-table 
                            :data="abnormalTransactions" 
                            stripe 
                            style="width: 100%" 
                            size="small"
                            height="350"
                            :virtual-scrolling="true"
                            :scroll-options="{ scrollSpeed: 15 }"
                            v-loading="tableLoading"
                        >
                            <el-table-column prop="id" label="交易ID" width="140" />
                            <el-table-column prop="user" label="用户" width="100" />
                            <el-table-column prop="amount" label="金额" width="120">
                                <template #default="scope">
                                    <span class="amount negative">
                                        <el-icon v-if="scope.row.riskLevel === '高'" class="warning-icon"><WarningFilled /></el-icon>
                                        {{ scope.row.amount.toLocaleString() }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="riskLevel" label="风险等级" width="100">
                                <template #default="scope">
                                    <el-tag :type="getRiskLevelColor(scope.row.riskLevel)" size="small">
                                        {{ scope.row.riskLevel }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="reason" label="异常原因" />
                            <el-table-column prop="time" label="时间" width="160" />
                            <el-table-column label="操作" width="80">
                                <template #default="scope">
                                    <el-button type="text" size="small" @click="reviewTransaction(scope.row)">
                                        审核
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </el-col>

                <!-- 实时交易列表 -->
                <el-col :span="12">
                    <el-card class="transaction-card" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span class="header-title">实时交易记录</span>
                                <div class="header-actions">
                                    <el-input
                                        v-model="searchKeyword"
                                        placeholder="搜索交易ID或用户"
                                        size="small"
                                        style="width: 200px; margin-right: 12px;"
                                        clearable
                                    >
                                        <template #prefix>
                                            <el-icon><Search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-button type="primary" size="small" @click="viewAllTransactions">
                                        查看全部
                                    </el-button>
                                </div>
                            </div>
                        </template>
                        <el-table 
                            :data="filteredTransactions" 
                            stripe 
                            style="width: 100%" 
                            size="small"
                            height="350"
                            :virtual-scrolling="true"
                            :scroll-options="{ scrollSpeed: 15 }"
                            v-loading="tableLoading"
                        >
                            <el-table-column prop="id" label="交易ID" width="140" />
                            <el-table-column prop="user" label="用户" width="100" />
                            <el-table-column prop="type" label="交易类型" width="100">
                                <template #default="scope">
                                    <el-tag :type="getTransactionTypeColor(scope.row.type)" size="small">
                                        {{ scope.row.type }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="amount" label="金额" width="120">
                                <template #default="scope">
                                    <span class="amount" :class="scope.row.amount > 0 ? 'positive' : 'negative'">
                                        {{ scope.row.amount > 0 ? '+' : '' }}{{ scope.row.amount.toLocaleString() }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template #default="scope">
                                    <el-tag :type="getStatusColor(scope.row.status)" size="small">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="time" label="时间" width="160" />
                            <el-table-column label="操作" width="80">
                                <template #default="scope">
                                    <el-button type="text" size="small" @click="viewTransactionDetail(scope.row)">
                                        详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 设置对话框 -->
        <el-dialog v-model="settingsVisible" title="看板设置" width="500px">
            <el-form :model="settings" label-width="100px">
                <el-form-item label="自动刷新">
                    <el-switch v-model="settings.autoRefresh" />
                </el-form-item>
                <el-form-item label="刷新间隔">
                    <el-select v-model="settings.refreshInterval" :disabled="!settings.autoRefresh">
                        <el-option label="30秒" value="30" />
                        <el-option label="1分钟" value="60" />
                        <el-option label="5分钟" value="300" />
                    </el-select>
                </el-form-item>
                <el-form-item label="显示项目">
                    <el-checkbox-group v-model="settings.displayItems">
                        <el-checkbox label="overview">数据概览</el-checkbox>
                        <el-checkbox label="charts">图表分析</el-checkbox>
                        <el-checkbox label="risk">风险监控</el-checkbox>
                        <el-checkbox label="transactions">交易记录</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="settingsVisible = false">取消</el-button>
                <el-button type="primary" @click="saveSettings">保存</el-button>
            </template>
        </el-dialog>

        <!-- 风控阈值设置弹窗 -->
        <el-dialog v-model="riskThresholdDialogVisible" title="设置平台风控阈值" width="350px">
            <el-form>
                <el-form-item label="阈值金额">
                    <el-input-number
                        v-model="riskThresholdInput"
                        :min="0"
                        :step="1000000"
                        :precision="0"
                        controls-position="right"
                        style="width: 200px"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="riskThresholdDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveRiskThreshold">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import Vue from 'vue'
import { Message, MessageBox } from 'element-ui'
import {
    Refresh,
    Download,
    Money,
    TrendCharts,
    User,
    ArrowDown,
    Wallet,
    ArrowUp,
    Setting,
    Search,
    Warning
} from 'element-ui/lib/icon'
import { use } from 'echarts/core'
import VChart from 'vue-echarts'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent
} from 'echarts/components'

use([
    CanvasRenderer,
    LineChart,
    PieChart,
    BarChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent
])

export default {
    data() {
        return {
             // 其他数据...
            defaultChartOption: {
                title: {
                    text: '暂无数据',
                    left: 'center',
                    top: 'center'
                },
                tooltip: {
                    formatter: '暂无数据'
                }
            },
            chartLoading: false,
            tableLoading: false,
            trendTimeRange: '7d',
            dateRange: [],
            dateShortcuts: [
                {
                    text: '最近一周',
                    value: () => {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                        return [start, end]
                    }
                },
                {
                    text: '最近一个月',
                    value: () => {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        return [start, end]
                    }
                },
                {
                    text: '最近三个月',
                    value: () => {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        return [start, end]
                    }
                }
            ],
            userActivityType: 'daily',
            incomeType: 'daily',
            searchKeyword: '',
            settingsVisible: false,
            settings: {
                autoRefresh: false,
                refreshInterval: '60',
                displayItems: ['overview', 'charts', 'risk', 'transactions']
            },
            notifications: [
                {
                    id: 1,
                    title: '系统维护通知',
                    message: '系统将于今晚22:00-24:00进行维护升级',
                    type: 'warning',
                    time: '10分钟前',
                    read: false
                },
                {
                    id: 2,
                    title: '异常交易提醒',
                    message: '检测到3笔异常交易，请及时处理',
                    type: 'error',
                    time: '30分钟前',
                    read: false
                },
                {
                    id: 3,
                    title: '数据备份完成',
                    message: '今日数据备份已完成，备份文件大小: 2.3GB',
                    type: 'success',
                    time: '1小时前',
                    read: true
                },
                {
                    id: 4,
                    title: '新用户注册',
                    message: '今日新增用户156人，较昨日增长12%',
                    type: 'info',
                    time: '2小时前',
                    read: true
                }
            ],
            quickActions: [
                {
                    name: '数据导出',
                    icon: 'Download',
                    color: '#409EFF',
                    action: 'export'
                },
                {
                    name: '风险处理',
                    icon: 'Warning',
                    color: '#F56C6C',
                    action: 'risk'
                },
                {
                    name: '用户管理',
                    icon: 'User',
                    color: '#67C23A',
                    action: 'user'
                },
                {
                    name: '系统设置',
                    icon: 'Setting',
                    color: '#E6A23C',
                    action: 'setting'
                },
                {
                    name: '报表生成',
                    icon: 'Document',
                    color: '#909399',
                    action: 'report'
                },
                {
                    name: '监控面板',
                    icon: 'Monitor',
                    color: '#FF9800',
                    action: 'monitor'
                }
            ],
            userBehaviorData: [
                {
                    name: '页面访问量',
                    value: '45,678',
                    percentage: 85,
                    color: '#409EFF'
                },
                {
                    name: '用户停留时间',
                    value: '12.5分钟',
                    percentage: 72,
                    color: '#67C23A'
                },
                {
                    name: '转化率',
                    value: '23.4%',
                    percentage: 23,
                    color: '#E6A23C'
                },
                {
                    name: '跳出率',
                    value: '34.2%',
                    percentage: 34,
                    color: '#F56C6C'
                }
            ],
            businessMetrics: [
                {
                    name: '客户满意度',
                    value: '4.8/5.0',
                    trend: 5.2,
                    description: '基于用户反馈评分'
                },
                {
                    name: '交易成功率',
                    value: '99.2%',
                    trend: 0.8,
                    description: '成功交易占总交易比例'
                },
                {
                    name: '平均响应时间',
                    value: '125ms',
                    trend: -12.5,
                    description: '系统平均响应时间'
                },
                {
                    name: '系统可用性',
                    value: '99.9%',
                    trend: 0.1,
                    description: '系统运行时间占比'
                }
            ],
            systemPerformance: [
                {
                    name: 'CPU使用率',
                    value: '45%',
                    status: 'normal',
                    data: [45, 52, 38, 41, 48, 43, 45]
                },
                {
                    name: '内存使用率',
                    value: '78%',
                    status: 'warning',
                    data: [78, 82, 75, 79, 81, 77, 78]
                },
                {
                    name: '磁盘使用率',
                    value: '65%',
                    status: 'normal',
                    data: [65, 67, 63, 66, 68, 64, 65]
                }
            ],
            systemStatus: {
                type: 'success',
                text: '运行正常'
            },
            overviewCards: [
                {
                    title: '总交易额',
                    value: '¥1,234,567,890',
                    trend: 12.5,
                    icon: 'Money',
                    color: '#409EFF',
                    subtitle: '较昨日增长',
                    detail: '交易笔数: 12,345'
                },
                {
                    title: '今日交易额',
                    value: '¥45,678,901',
                    trend: 8.3,
                    icon: 'TrendCharts',
                    color: '#67C23A',
                    subtitle: '较昨日增长',
                    detail: '交易笔数: 1,234'
                },
                {
                    title: '活跃用户数',
                    value: '12,345',
                    trend: -2.1,
                    icon: 'User',
                    color: '#E6A23C',
                    subtitle: '较昨日变化',
                    detail: '新增用户: 156'
                },
                {
                    title: '账户余额',
                    value: '¥89,012,345',
                    trend: 15.7,
                    icon: 'Wallet',
                    color: '#F56C6C',
                    subtitle: '较昨日增长',
                    detail: '冻结金额: ¥2,345,678'
                },
                {
                    title: '手续费收入',
                    value: '¥2,345,678',
                    trend: 5.2,
                    icon: 'Money',
                    color: '#909399',
                    subtitle: '较昨日增长',
                    detail: '费率: 0.1%'
                },
                {
                    title: '风险交易',
                    value: '23',
                    trend: -15.3,
                    icon: 'Warning',
                    color: '#FF9800',
                    subtitle: '较昨日减少',
                    detail: '处理中: 5笔'
                }
            ],
            riskThreshold: 1000000000,
            riskThresholdDialogVisible: false,
            riskThresholdInput: 1000000000,
            riskIndicators: [
                {
                    name: '异常交易率',
                    value: '2.3%',
                    percentage: 23,
                    level: 'normal',
                    color: '#67C23A'
                },
                {
                    name: '资金风险指数',
                    value: '15.6',
                    percentage: 15,
                    level: 'normal',
                    color: '#E6A23C'
                },
                {
                    name: '系统负载',
                    value: '78.2%',
                    percentage: 78,
                    level: 'warning',
                    color: '#F56C6C'
                },
                {
                    name: '响应时间',
                    value: '125ms',
                    percentage: 85,
                    level: 'normal',
                    color: '#409EFF'
                }
            ],
            abnormalTransactions: [
                {
                    id: 'TX20241201006',
                    user: '异常用户A',
                    amount: -500000,
                    riskLevel: '高风险',
                    reason: '大额异常提现',
                    time: '2024-12-01 10:15:30'
                },
                {
                    id: 'TX20241201007',
                    user: '异常用户B',
                    amount: -300000,
                    riskLevel: '中风险',
                    reason: '频繁小额交易',
                    time: '2024-12-01 10:12:45'
                },
                {
                    id: 'TX20241201008',
                    user: '异常用户C',
                    amount: -150000,
                    riskLevel: '低风险',
                    reason: '异地登录交易',
                    time: '2024-12-01 10:10:20'
                },
                {
                    id: 'TX20241201009',
                    user: '异常用户D',
                    amount: -120000,
                    riskLevel: '低风险',
                    reason: '异常时间交易',
                    time: '2024-12-01 10:08:15'
                },
                {
                    id: 'TX20241201010',
                    user: '异常用户E',
                    amount: -80000,
                    riskLevel: '低风险',
                    reason: 'IP地址异常',
                    time: '2024-12-01 10:05:30'
                }
            ],
            recentTransactions: [
                {
                    id: 'TX20241201001',
                    user: '张三',
                    type: '充值',
                    amount: 50000,
                    status: '成功',
                    time: '2024-12-01 10:30:25'
                },
                {
                    id: 'TX20241201002',
                    user: '李四',
                    type: '提现',
                    amount: -20000,
                    status: '处理中',
                    time: '2024-12-01 10:28:15'
                },
                {
                    id: 'TX20241201003',
                    user: '王五',
                    type: '充值',
                    amount: 15000,
                    status: '成功',
                    time: '2024-12-01 10:25:42'
                },
                {
                    id: 'TX20241201004',
                    user: '赵六',
                    type: '多单',
                    amount: -8000,
                    status: '成功',
                    time: '2024-12-01 10:22:18'
                },
                {
                    id: 'TX20241201005',
                    user: '钱七',
                    type: '空单',
                    amount: 3000,
                    status: '成功',
                    time: '2024-12-01 10:20:33'
                }
            ]
        }
    },
    computed: {
        unreadNotifications() {
            return this.notifications.filter(n => !n.read)
        },
        allCards() {
            return [
                { isRisk: true },
                ...this.overviewCards
            ]
        },
        filteredTransactions() {
            if (!this.searchKeyword) return this.recentTransactions
            const keyword = this.searchKeyword.toLowerCase()
            return this.recentTransactions.filter(item =>
                item.id.toLowerCase().includes(keyword) ||
                item.user.toLowerCase().includes(keyword) ||
                item.type.toLowerCase().includes(keyword) ||
                item.amount.toString().includes(this.searchKeyword)
            )
        },
        trendChartOption() {
            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>'
                        params.forEach((param) => {
                            const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`
                            if (param.seriesName === '交易金额') {
                                result += marker + param.seriesName + ': ¥' + (param.value / 10000).toFixed(2) + '万<br/>'
                            } else {
                                result += marker + param.seriesName + ': ' + param.value + '笔<br/>'
                            }
                        })
                        return result
                    }
                },
                legend: {
                    data: ['交易金额', '交易笔数', '手续费收入'],
                    top: 8,
                    textStyle: {
                        fontSize: 12
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    axisLine: {
                        lineStyle: {
                            color: '#E4E7ED'
                        }
                    },
                    axisLabel: {
                        color: '#606266',
                        fontSize: 11
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '金额(万元)',
                        position: 'left',
                        axisLine: {
                            lineStyle: {
                                color: '#E4E7ED'
                            }
                        },
                        axisLabel: {
                            color: '#606266',
                            fontSize: 11,
                            formatter: '{value}万'
                        },
                        nameTextStyle: {
                            fontSize: 11,
                            color: '#909399'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#F5F7FA'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '笔数',
                        position: 'right',
                        axisLine: {
                            lineStyle: {
                                color: '#E4E7ED'
                            }
                        },
                        axisLabel: {
                            color: '#606266',
                            fontSize: 11
                        },
                        nameTextStyle: {
                            fontSize: 11,
                            color: '#909399'
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '交易金额',
                        type: 'line',
                        smooth: true,
                        data: [150, 180, 210, 240, 270, 300, 330],
                        itemStyle: {
                            color: '#409EFF'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                                    { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                                ]
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        }
                    },
                    {
                        name: '交易笔数',
                        type: 'line',
                        yAxisIndex: 1,
                        smooth: true,
                        data: [200, 220, 240, 260, 280, 300, 320],
                        itemStyle: {
                            color: '#67C23A'
                        },
                        emphasis: {
                            focus: 'series'
                        }
                    },
                    {
                        name: '手续费收入',
                        type: 'line',
                        smooth: true,
                        data: [15, 18, 21, 24, 27, 30, 33],
                        itemStyle: {
                            color: '#E6A23C'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(230, 162, 60, 0.2)' },
                                    { offset: 1, color: 'rgba(230, 162, 60, 0.05)' }
                                ]
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        }
                    }
                ],
                animation: true,
                animationDuration: 1000
            }
        },
        pieChartOption() {
            return {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    top: 'center',
                    textStyle: {
                        fontSize: 11
                    }
                },
                series: [
                    {
                        name: '交易类型',
                        type: 'pie',
                        radius: ['35%', '65%'],
                        center: ['65%', '50%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '16',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 1500, name: '充值', itemStyle: { color: '#409EFF' } },
                            { value: 1200, name: '提现', itemStyle: { color: '#67C23A' } },
                            { value: 900, name: '兑换', itemStyle: { color: '#E6A23C' } },
                            { value: 600, name: '调账', itemStyle: { color: '#F56C6C' } },
                            { value: 300, name: '冲正', itemStyle: { color: '#909399' } }
                        ]
                    }
                ]
            }
        },
        userActivityOption() {
            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '8%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    axisLabel: {
                        fontSize: 11,
                        color: '#606266'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#E4E7ED'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '活跃用户数',
                    axisLabel: {
                        fontSize: 11,
                        color: '#606266'
                    },
                    nameTextStyle: {
                        fontSize: 11,
                        color: '#909399'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#E4E7ED'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#F5F7FA'
                        }
                    }
                },
                series: [
                    {
                        name: '活跃用户',
                        type: 'bar',
                        data: [100, 150, 200, 250, 300, 350, 400],
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    { offset: 0, color: '#409EFF' },
                                    { offset: 1, color: '#67C23A' }
                                ]
                            }
                        }
                    }
                ]
            }
        },
        incomeChartOption() {
            return {
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '8%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    axisLabel: {
                        fontSize: 11,
                        color: '#606266'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#E4E7ED'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '收入(万元)',
                    axisLabel: {
                        fontSize: 11,
                        color: '#606266'
                    },
                    nameTextStyle: {
                        fontSize: 11,
                        color: '#909399'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#E4E7ED'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#F5F7FA'
                        }
                    }
                },
                series: [
                    {
                        name: '平台收入',
                        type: 'line',
                        smooth: true,
                        data: [150, 230, 224, 218, 135, 147, 180, 210, 240, 270, 300, 330],
                        itemStyle: {
                            color: '#E6A23C'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(230, 162, 60, 0.3)' },
                                    { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }
                                ]
                            }
                        }
                    }
                ]
            }
        },
        getPerformanceChartOption() {
            return (item) => ({
                grid: {
                    top: 5,
                    right: 5,
                    bottom: 5,
                    left: 5
                },
                xAxis: {
                    type: 'category',
                    show: false,
                    data: ['', '', '', '', '', '', '']
                },
                yAxis: {
                    type: 'value',
                    show: false
                },
                series: [
                    {
                        data: item.data,
                        type: 'line',
                        smooth: true,
                        symbol: 'none',
                        lineStyle: {
                            color: item.status === 'warning' ? '#F56C6C' : '#67C23A',
                            width: 2
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    { 
                                        offset: 0, 
                                        color: item.status === 'warning' ? 'rgba(245, 108, 108, 0.3)' : 'rgba(103, 194, 58, 0.3)' 
                                    },
                                    { 
                                        offset: 1, 
                                        color: item.status === 'warning' ? 'rgba(245, 108, 108, 0.1)' : 'rgba(103, 194, 58, 0.1)' 
                                    }
                                ]
                            }
                        }
                    }
                ]
            })
        }
    },
    methods: {
        refreshData() {
            this.$message.info('正在刷新数据...')
            this.fetchChartData()
            this.fetchTableData()
            setTimeout(() => {
                try {
                    this.$message.success('数据已刷新')
                } catch (error) {
                    this.$message.error('刷新数据失败，请重试')
                }
            }, 1000)
        },
        exportData() {
            this.$confirm('请选择要导出的数据', '导出报表', {
                confirmButtonText: '导出全部',
                cancelButtonText: '取消',
                distinguishCancelAndClose: true,
                type: 'info',
                showClose: true,
                closeOnClickModal: false
            }).then(() => {
                this.handleExport('all')
            }).catch(action => {
                if (action === 'cancel') {
                    this.$message.info('已取消导出')
                }
            })
        },
        handleExport(type) {
            const exportTypes = {
                'all': '全部数据',
                'chart': '图表数据',
                'table': '表格数据'
            }
            
            const loading = this.$loading({
                lock: true,
                text: `正在导出${exportTypes[type]}，请稍候...`,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            
            setTimeout(() => {
                loading.close()
                this.$message.success(`${exportTypes[type]}导出成功`)
            }, 1500)
        },
        saveSettings() {
            this.$message.success('设置已保存')
            this.settingsVisible = false
        },
        viewAllTransactions() {
            this.$message.info('跳转到交易记录页面')
        },
        viewTransactionDetail(transaction) {
            this.$message.info(`查看交易详情: ${transaction.id}`)
        },
        handleRiskAlert() {
            this.$confirm('确定要处理所有异常交易吗？', '确认操作', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('异常交易已处理')
            })
        },
        reviewTransaction(transaction) {
            this.$message.info(`审核交易: ${transaction.id}`)
        },
        getTransactionTypeColor(type) {
            const colorMap = {
                '充值': 'success',
                '提现': 'warning',
                '转账': 'primary',
                '支付': 'danger',
                '退款': 'info'
            }
            return colorMap[type] || 'info'
        },
        getStatusColor(status) {
            const colorMap = {
                '成功': 'success',
                '处理中': 'warning',
                '失败': 'danger',
                '待审核': 'info'
            }
            return colorMap[status] || 'info'
        },
        getRiskLevelColor(level) {
            const colorMap = {
                '高风险': 'danger',
                '中风险': 'warning',
                '低风险': 'info'
            }
            return colorMap[level] || 'info'
        },
        markAsRead(notification) {
            notification.read = true
            this.$message.success('已标记为已读')
        },
        markAllAsRead() {
            this.notifications.forEach(n => n.read = true)
            this.$message.success('全部标记为已读')
        },
        getNotificationIcon(type) {
            const iconMap = {
                'success': 'CircleCheck',
                'warning': 'Warning',
                'error': 'CircleClose',
                'info': 'InfoFilled'
            }
            return iconMap[type] || 'InfoFilled'
        },
        executeQuickAction(action) {
            switch (action.action) {
                case 'export':
                    this.$message.success('开始导出数据')
                    break
                case 'risk':
                    this.$message.info('跳转到风险处理页面')
                    break
                case 'user':
                    this.$message.info('跳转到用户管理页面')
                    break
                case 'setting':
                    this.$message.info('跳转到系统设置页面')
                    break
                case 'report':
                    this.$message.success('开始生成报表')
                    break
                case 'monitor':
                    this.$message.info('跳转到监控面板')
                    break
                default:
                    this.$message.info(`执行操作: ${action.name}`)
            }
        },
        handleUserBehaviorExport(command) {
            if (command === 'excel') {
                this.$message.success('用户行为数据已导出为Excel')
            } else if (command === 'pdf') {
                this.$message.success('用户行为数据已导出为PDF')
            }
        },
        showRiskThresholdDialog() {
            this.riskThresholdInput = this.riskThreshold
            this.riskThresholdDialogVisible = true
        },
        saveRiskThreshold() {
            if (this.riskThresholdInput < 0 || isNaN(this.riskThresholdInput)) {
                this.$message.error('请输入有效的正数阈值')
                return
            }
            this.riskThreshold = this.riskThresholdInput
            this.riskThresholdDialogVisible = false
            this.$message.success('风控阈值已更新')
        },
        handleChartError(e) {
            console.error('图表渲染错误:', e)
            this.$message.error('图表数据加载失败，请稍后重试')
        },
        fetchChartData() {
            this.chartLoading = true
            // 模拟异步数据加载
            setTimeout(() => {
                this.chartLoading = false
            }, 800)
        },
        fetchTableData() {
            this.tableLoading = true
            // 模拟异步数据加载
            setTimeout(() => {
                this.tableLoading = false
            }, 800)
        }
    },
    mounted() {
        this.fetchChartData()
        this.fetchTableData()
    }
}
</script>

<style scoped>
    .workbench {
        padding: 16px;
        background-color: #f5f7fa;
        min-height: 100vh;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #ebeef5;

        .header-left {
            .page-title {
                font-size: 20px;
                color: #303133;
                margin: 0 0 4px 0;
                font-weight: 600;
            }

            .page-subtitle {
                font-size: 13px;
                color: #909399;
                margin: 0;
            }
        }

        .page-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .overview-cards {
        margin-bottom: 16px;
        .overview-card {
            min-height: 110px;
            display: flex;
            flex-direction: column;
            height: 100%;
            border-radius: 14px;
            box-shadow: 0 2px 8px rgba(64,158,255,0.06);
            transition: box-shadow 0.2s, transform 0.2s;
            border: none;
            &:hover {
                box-shadow: 0 4px 16px rgba(64,158,255,0.12);
                transform: translateY(-2px) scale(1.02);
            }
            .card-content {
                display: flex;
                align-items: center;
                padding: 16px 12px;
                .card-icon {
                    width: 38px;
                    height: 38px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 10px;
                    background: rgba(64,158,255,0.08);
                }
                .card-info {
                    flex: 1;
                    min-width: 0;
                    .card-title {
                        font-size: 12px;
                        color: #909399;
                        margin-bottom: 2px;
                    }
                    .card-value {
                        font-size: 18px;
                        font-weight: bold;
                        color: #222;
                        margin-bottom: 2px;
                    }
                    .card-trend {
                        display: inline-flex;
                        align-items: center;
                        font-size: 11px;
                        border-radius: 8px;
                        padding: 0 6px;
                        margin-top: 2px;
                        &.up {
                            background: #f0f9eb;
                            color: #67C23A;
                        }
                        &.down {
                            background: #fef0f0;
                            color: #F56C6C;
                        }
                    }
                    .card-detail {
                        font-size: 11px;
                        color: #b0b0b0;
                        margin-top: 2px;
                    }
                }
            }
            .el-button {
                border-radius: 8px;
                background: #f4f8ff;
                color: #409EFF;
                border: none;
                font-size: 12px;
                margin-top: 4px;
                &:hover {
                    background: #e6f0ff;
                }
            }
        }
    }

    .main-charts, .secondary-charts {
        margin-bottom: 16px;

        .chart-card {
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;

                .header-title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                }
            }

            .chart-container {
                height: 280px;
                padding: 16px;

                .chart {
                    height: 100%;
                }
            }
        }
    }

    .monitor-section {
        margin-bottom: 16px;

        .monitor-card, .notification-card, .quick-actions-card {
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;

                .header-title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                }
            }
        }

        .monitor-card {
            .risk-indicators {
                padding: 16px;

                .risk-item {
                    margin-bottom: 12px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .risk-info {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 6px;

                        .risk-name {
                            font-size: 13px;
                            color: #606266;
                        }

                        .risk-value {
                            font-weight: 600;
                            font-size: 14px;

                            &.normal {
                                color: #67C23A;
                            }

                            &.warning {
                                color: #E6A23C;
                            }

                            &.danger {
                                color: #F56C6C;
                            }
                        }
                    }
                }
            }
        }

        .notification-card {
            .notification-list {
                max-height: 200px;
                overflow-y: auto;
                padding: 8px 0;

                .notification-item {
                    display: flex;
                    align-items: center;
                    padding: 8px 16px;
                    border-bottom: 1px solid #f5f5f5;
                    cursor: pointer;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: #f8f9fa;
                    }

                    &:last-child {
                        border-bottom: none;
                    }

                    &.unread {
                        background-color: #f0f9ff;
                        border-left: 3px solid #409EFF;
                        margin-left: 0;
                    }

                    .notification-icon {
                        width: 28px;
                        height: 28px;
                        border-radius: 6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 10px;
                        flex-shrink: 0;

                        &.success {
                            background-color: #f0f9ff;
                            color: #67C23A;
                        }

                        &.warning {
                            background-color: #fdf6ec;
                            color: #E6A23C;
                        }

                        &.error {
                            background-color: #fef0f0;
                            color: #F56C6C;
                        }

                        &.info {
                            background-color: #f4f4f5;
                            color: #909399;
                        }
                    }

                    .notification-content {
                        flex: 1;
                        min-width: 0;

                        .notification-title {
                            font-size: 13px;
                            color: #303133;
                            margin-bottom: 2px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .notification-time {
                            font-size: 11px;
                            color: #909399;
                        }
                    }
                }
            }
        }

        .quick-actions-card {
            .quick-actions {
                padding: 16px;

                .action-grid {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 12px;

                    .action-item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        padding: 12px 8px;
                        border-radius: 6px;
                        cursor: pointer;
                        transition: all 0.2s;
                        border: 1px solid #f0f0f0;

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            border-color: #409EFF;
                        }

                        .action-icon {
                            width: 32px;
                            height: 32px;
                            border-radius: 8px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-bottom: 6px;
                        }

                        .action-name {
                            font-size: 11px;
                            color: #606266;
                            text-align: center;
                            line-height: 1.2;
                        }
                    }
                }
            }
        }
    }

    .transaction-monitor-section {
        margin-bottom: 16px;

        .abnormal-card, .transaction-card {
            height: 100%;
            min-height: 400px;
            display: flex;
            flex-direction: column;

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                flex-shrink: 0;

                .header-title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                }

                .header-actions {
                    display: flex;
                    align-items: center;
                }
            }

            .el-card__body {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding: 0;
            }

            .el-table {
                flex: 1;
                height: 100%;

                .el-table__body-wrapper {
                    height: calc(100% - 40px);
                }

                .el-table__row {
                    height: 48px;
                }
            }

            .amount {
                font-weight: 600;

                &.positive {
                    color: #67C23A;
                }

                &.negative {
                    color: #F56C6C;
                }
            }
        }
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .workbench {
            .overview-cards,
            .main-charts,
            .secondary-charts,
            .monitor-section,
            .transaction-monitor-section {
                .el-col {
                    margin-bottom: 12px;
                }
            }
        }
        .overview-cards .el-col {
            min-width: 160px;
            max-width: 1fr;
        }
    }

    @media (max-width: 768px) {
        .workbench {
            padding: 12px;

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .overview-cards {
                .overview-card {
                    .card-content {
                        .card-icon {
                            width: 36px;
                            height: 36px;
                        }

                        .card-info {
                            .card-value {
                                font-size: 14px;
                            }
                        }
                    }
                }
            }

            .monitor-section {
                .quick-actions-card {
                    .quick-actions {
                        .action-grid {
                            grid-template-columns: repeat(2, 1fr);
                        }
                    }
                }
            }

            .transaction-monitor-section {
                .abnormal-card, .transaction-card {
                    .card-header {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 8px;

                        .header-actions {
                            width: 100%;
                            justify-content: space-between;
                        }
                    }
                }
            }
        }
        .overview-cards .el-col {
            min-width: 100% !important;
            max-width: 100% !important;
        }
    }
</style>