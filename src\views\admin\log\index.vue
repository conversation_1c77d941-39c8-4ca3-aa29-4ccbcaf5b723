<!--
  -    Copyright (c) 2018-2025, lengleng All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the pig4cloud.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: lengleng (<EMAIL>)
  -->

<template>
  <div class="log">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        :permission="permissionList"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-del="handleDel"
      >
        <template slot="menuLeft">
          <el-button
            v-if="permissions.sys_log_del"
            class="filter-item"
            plain
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="exportExcel"
            >导出
          </el-button>
          <el-button
            class="filter-item"
            v-if="permissions.sys_log_del"
            plain
            type="primary"
            size="small"
            icon="el-icon-remove"
            @click="handleDelLogs"
          >批量删除
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import { delObj,delObjs, fetchList } from "@/api/admin/log";
import { tableOption } from "@/const/crud/admin/log";
import { mapGetters } from "vuex";

export default {
  name: "Log",
  data() {
    return {
      tableData: [],
      searchForm: {},
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20 // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
        console.log(this.vaildData(this.permissions.sys_log_del, false))
      return {
        delBtn: this.vaildData(this.permissions.sys_log_del, false)
      };
    }
  },
  methods: {
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
        Object.assign(
          {
            descs: ["create_time"],
            ascs: ["time"],
            current: page.currentPage,
            size: page.pageSize
          },
          params,
          this.searchForm
        )
      ).then(response => {
        this.tableData = response.data.data.records;
        this.page.total = response.data.data.total;
        this.tableLoading = false;
      });
    },
    handleDel: function(row) {
      this.$confirm('是否确认删除ID为"' + row.id + '"的日志?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return delObj(row.id);
        })
        .then(() => {
          this.getList(this.page);
          this.$message.success("删除成功");
        });
    },
    handleDelLogs() {
      if (this.$refs.crud.tableSelect.length === 0) {
        this.$message.error('请选择日志后进行删除')
        return false
      }
      let logIds = []
      for (const row of this.$refs.crud.tableSelect) {
        logIds.push(row.id)
      }
      delObjs(logIds).then(() => {
        this.$notify.success('删除成功')
        this.$refs.crud.toggleSelection();
        this.refreshChange();
      })
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    refreshChange() {
      this.getList(this.page);
    },
    exportExcel() {
      this.downBlobFile("/admin/log/export", this.searchForm, "log.xlsx");
    }
  }
};
</script>
