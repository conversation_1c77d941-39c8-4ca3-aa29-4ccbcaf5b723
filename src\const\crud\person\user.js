export const tableOption = {
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  searchMenuSpan: 6,
  viewBtn: true,
  editBtn: false,
  delBtn: false,
  align: 'center',
  addBtn: false,
  column: [
    {
      fixed: true,
      label: 'id',
      prop: 'userId',
      span: 24,
      hide: true,
      editDisabled: true,
      addDisplay: false
    },
    {
      fixed: true,
      label: '用户名',
      prop: 'nickname',
      editDisabled: true,
      search: true,
      span: 24
    },
    {
      label: '用户类型',
      prop: 'userTypeDesc',
      value: '',
      span: 24
    },
    {
      label: '手机号',
      prop: 'phone',
      value: '',
      span: 24,
      rules: [
        {
          min: 11,
          max: 11,
          required: true,
          message: '长度在 11 个字符',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '状态',
      prop: 'userStatusDesc',
      type: 'radio',
      border: true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择状态',
          trigger: 'blur'
        }
      ],
      dicData: [
        {
          label: '有效',
          value: '0'
        },
        {
          label: '锁定',
          value: '9'
        }
      ]
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      editDisabled: true,
      addDisplay: false,
      span: 24
    }
  ]
}