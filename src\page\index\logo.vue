<template>
  <div class="avue-logo">
    <transition-group name="fade">
      <div v-if="!keyCollapse" key="1" class="avue-logo_container" @click="goIndex">
        <img class="avue-logo_img" src="/img/logo.png" />
        <span class="avue-logo_title">{{ website.title }}</span>
      </div>
      <div v-else key="2" class="avue-logo_container collapsed" @click="goIndex">
        <img class="avue-logo_img" src="/img/logo.png" />
      </div>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'Logo',
  computed: {
    ...mapGetters(['website', 'keyCollapse'])
  },
  methods: {
    goIndex: function () {
      this.$store.commit('REMOVE_LIKE_TOP_MENUID')
      window.location.href = '/'
    }
  }
}
</script>

<style lang="scss">
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.avue-logo {
  position: fixed;
  top: 0;
  left: 0;
  width: 240px;
  height: 64px;
  line-height: 64px;
  background-color: #20222a;
  font-size: 20px;
  overflow: hidden;
  box-sizing: border-box;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &_container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    padding: 0 15px;

    &.collapsed {
      justify-content: center;
    }
  }

  &_img {
    height: 40px;
    object-fit: contain;
    margin-right: 10px;
    flex-shrink: 0;
  }

  &_title {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    display: inline-block;
  }
}
</style>