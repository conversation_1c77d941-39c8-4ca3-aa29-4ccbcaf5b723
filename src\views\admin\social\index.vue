<!--
  -    Copyright (c) 2018-2025, lengleng All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the pig4cloud.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: lengleng (<EMAIL>)
  -->

<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        :permission="permissionList"
        @on-load="getList"
        :before-open="beforeOpen"
        @refresh-change="refreshChange"
        @search-change="searchChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"/>
    </basic-container>
  </div>
</template>

<script>
import {addObj, delObj, fetchList, putObj} from '@/api/admin/sys-social-details'
import {tableOption} from '@/const/crud/admin/sys-social-details'
import {mapGetters} from 'vuex'

export default {
  name: 'SysSocialDetails',
  data() {
    return {
      searchForm: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20 // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption
    }
  },
  created() {
  },
  mounted: function () {
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.sys_social_details_add, false),
        delBtn: this.vaildData(this.permissions.sys_social_details_del, false),
        editBtn: this.vaildData(this.permissions.sys_social_details_edit, false)
      }
    }
  },
  methods: {
    getList(page, params) {
      this.tableLoading = true
      fetchList(Object.assign({
        current: page.currentPage,
        size: page.pageSize
      }, params, this.searchForm)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.tableLoading = false
      })
    },
    rowDel: function (row, index) {
      var _this = this
      this.$confirm('是否确认删除ID为' + row.id, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(() => {
        _this.$message.success('删除成功')
        this.refreshChange()
      }).catch(function () {
      })
    },
    handleUpdate: function (row, index, done) {
      if (row.appSecret && row.appSecret.indexOf("*") >= 0) {
        row.appSecret = undefined;
      }
      if (row.appId && row.appId.indexOf("*") >= 0) {
        row.appId = undefined;
      }
      putObj(row).then(() => {
        this.$message.success('修改成功')
        this.refreshChange()
        done()
      }).catch(() => {
        loading()
      })
    },
    handleSave: function (row, done, loading) {
      addObj(row).then(() => {
        this.tableData.push(Object.assign({}, row))
        this.$message.success('添加成功')
        this.refreshChange()
        done()
      }).catch(() => {
        loading()
      })
    },
    refreshChange() {
      this.getList(this.page)
    },
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    currentChange(current) {
      this.page.currentPage = current
    },
    beforeOpen(show, type) {
      window.boxType = type
      show()
    }
  }
}
</script>

