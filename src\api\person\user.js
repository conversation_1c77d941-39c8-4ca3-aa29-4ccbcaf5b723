import request from '@/router/axios'

// 分页查询用户信息
export function fetchList(query) {
  return request({
    url: '/user/cms/user/page',
    method: 'post',
    data: query
  })
}

// 添加用户
export function addObj(obj) {
  return request({
    url: '/user/cms/user',
    method: 'post',
    data: obj
  })
}

// 更新用户
export function putObj(obj) {
  return request({
    url: '/user/cms/user',
    method: 'put',
    data: obj
  })
}

// 删除用户
export function delObj(id) {
  return request({
    url: '/user/cms/user/' + id,
    method: 'delete'
  })
}

// 导出用户信息
export function exportExcel(query) {
  return request({
    url: '/user/cms/user/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 分页查询用户实名认证记录
export function queryRealNameRecordPage(query) {
  return request({
    url: '/user/cms/user/real_name_record/page',
    method: 'post',
    data: query
  })
}

// 审核用户实名认证
export function auditRealName(data) {
  return request({
    url: '/user/cms/user/audit/real_name',
    method: 'post',
    data: data
  })
}

// 重置用户实名认证
export function resetRealName(data) {
  return request({
    url: '/user/cms/user/reset/real_name',
    method: 'post',
    data: data
  })
}

// 查询实名认证业务列表
export function queryRealNameBusinessList() {
  return request({
    url: '/user/cms/user/real_name_business/list',
    method: 'post'
  })
}

// 配置实名认证业务
export function configureRealNameBusiness(data) {
  return request({
    url: '/user/cms/user/real_name_business/configure',
    method: 'post',
    data: data
  })
}

// 编辑用户业务开关
export function editUserBusinessSwitch(data) {
  return request({
    url: '/user/cms/user/edit/business_switch',
    method: 'post',
    data: data
  })
}

// 修改用户账号冻结状态
export function editAccountFreezeStatus(data) {
  return request({
    url: '/usercms/user/edit/freeze_status',
    method: 'post',
    data: data
  })
}