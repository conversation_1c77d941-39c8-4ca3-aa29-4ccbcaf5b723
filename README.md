## 协议和授权

pigX并非一个开源软件，作者保留全部的权利。
擅自窃用，即属严重侵权行为，与盗窃无异。产生的一切任何后果责任由侵权者自负。

### 🚫禁止  

将本项目的部分或全部代码和资源进行任何形式的再发行（上传GitHub、Gitee ）

### 侵权处理

- 支付￥ 100000 侵权费用（含我方法律援助费用）,本团队已完全委托博睿律师事务所 司法保护
- 个人行为将直接收集材料邮寄贵司侵权律师函，若出现司法诉讼将直接影响当事人征信档案等特此说明

## 贡献代码

pigX并非一个开源项目，也不是社区共同创造，其全部功能由作者独立完成。

如果你愿意放弃所有权利，并将权利无条件转让给pigX作者，欢迎您贡献代码,当然会给予不等金额奖励。

## 提交反馈

1. 欢迎提交 issue，请写清楚遇到问题的原因，浏览器和操作系统环境，重现的流程。
如果有开发能力，建议在本地调试出出错的代码。

2. 不接受`功能请求`的 issue，功能请求可能会被直接关闭，请谅解（正确的方式是打赏并附言）。

