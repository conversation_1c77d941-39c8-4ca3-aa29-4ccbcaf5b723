.theme-white {
  .avue-header,
  .avue-logo,
  .tags-container {
      background-color: #2c3e50;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  .avue-sidebar--tip{
    background-color:transparent;
    color:#333;
  }
  .el-dropdown{
    color:#fff;
  }
  .avue-logo_title{
    font-weight: 400;
    color:#fff;
    display: inline-block;
  }
  .logo_title,
  .avue-breadcrumb
  {
      color: #fff ;
      i {
          color: #fff;
      }
  }
  .avue-top{
    .el-menu-item {
      i,
      span {
          color: #fff ;
          font-weight: 500;
          transition: all 0.3s ease;
      }
      &:hover {
          i,
          span {
              color: #fff ;
              opacity: 0.9;
              transform: translateY(-2px);
          }
      }
    }
  }
  .el-menu--popup{
    background-color: #fff ;
    .el-menu-item{
      background-color: #fff ;
      span,i {
        color:#666 ;
      }
      &.is-active{
        background-color:#409EFF ;
        span,i {
          color:#fff ;
        }
      }
    }
  }
  .avue-sidebar{
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    .el-menu{
      background-color: #f8f9fa ;
    }
    background-color:#f8f9fa ;
    .el-menu-item,.el-submenu__title{
      i,span{
        color:#495057 ;
      }
      background-color:#f8f9fa ;
      position: relative;

      // 白色主题的阴影分割线
      &:not(:last-child)::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 20px;
        right: 20px;
        height: 1px;
        background: linear-gradient(90deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.05) 20%,
            rgba(0, 0, 0, 0.1) 50%,
            rgba(0, 0, 0, 0.05) 80%,
            rgba(0, 0, 0, 0) 100%
        );
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }
    .el-menu-item,.el-submenu__title{
      &:hover{
        background-color: transparent ;
        i,span{
          color:#666 ;
        }
      }
    }
    .el-menu-item.is-active,.el-submenu__title.is-active{
      background-color:#2c3e50 ;
      i,span{
        color:#fff ;
        font-weight: 500;
      }
      &::before{
        display: none;
      }
      &:hover{
        background-color: #34495e ;
        i,span{
          color:#fff ;
        }
      }
    }
  }
  .top-search {
      .el-input__inner{
        color: #fff;
      }
      input::-webkit-input-placeholder,
      textarea::-webkit-input-placeholder {
          /* WebKit browsers */
          color: #fff;
      }
      input:-moz-placeholder,
      textarea:-moz-placeholder {
          /* Mozilla Firefox 4 to 18 */
          color: #fff;
      }
      input::-moz-placeholder,
      textarea::-moz-placeholder {
          /* Mozilla Firefox 19+ */
          color: #fff;
      }
      input:-ms-input-placeholder,
      textarea:-ms-input-placeholder {
          /* Internet Explorer 10+ */
          color: #fff;
      }
  }
  .top-bar__item {
      i {
          color: #fff;
      }
  }
}
