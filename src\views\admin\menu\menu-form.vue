<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog :title="!form.menuId ? '新增': '修改'"
             :visible.sync="visible" append-to-body>
    <el-form ref="dataForm" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="菜单类型" prop="menuType">
            <el-radio-group v-model="form.menuType" size="small">
              <el-radio-button label="0">左菜单</el-radio-button>
              <el-radio-button label="1">按钮</el-radio-button>
              <el-radio-button label="2">顶菜单</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级菜单">
            <treeselect v-model="form.parentId"
                        :options="menuOptions"
                        :normalizer="normalizer"
                        :show-count="true"
                        placeholder="选择上级菜单"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="图标" prop="icon" v-if="form.menuType !== '1'">
        <avue-input-icon v-model="form.icon" :icon-list="iconList"></avue-input-icon>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入菜单名称" @blur="handleNameInput"/>
      </el-form-item>
      <el-form-item label="简体中文" prop="names[0].languageText">
        <el-input v-model="form.names[0].languageText" placeholder="请输入简体中文名称"/>
      </el-form-item>
      <el-form-item label="繁体中文" prop="names[1].languageText">
        <el-input v-model="form.names[1].languageText" placeholder="自动生成繁体中文名称" disabled/>
      </el-form-item>
      <el-form-item label="英文" prop="names[2].languageText">
        <el-input v-model="form.names[2].languageText" placeholder="自动生成英文名称" disabled/>
      </el-form-item>
      <el-form-item label="路由地址" prop="path" v-if="form.menuType !== '1'">
        <el-input v-model="form.path" placeholder="请输入路由地址"/>
      </el-form-item>
      <el-form-item label="权限标识" prop="permission" v-if="form.menuType === '1'">
        <el-input v-model="form.permission" placeholder="请权限标识" maxlength="50"/>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number v-model="form.sortOrder" controls-position="right" :min="0"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路由缓冲" prop="keepAlive" v-if="form.menuType !== '1'">
            <el-radio-group v-model="form.keepAlive">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dataFormSubmit">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {addObj, fetchMenuTree, getObj, putObj} from '@/api/admin/menu'
  import Treeselect from "@riophae/vue-treeselect"
  import iconList from '@/const/iconList'
  import TableForm from './'
  import "@riophae/vue-treeselect/dist/vue-treeselect.css"

  export default {
    name: "Menu",
    components: {Treeselect, TableForm},
    data() {
      return {
        // 遮罩层
        loading: true,
        // 菜单树选项
        menuOptions: [],
        // 是否显示弹出层
        visible: false,
        // 图标
        iconList: iconList,
        form: {
          menuId: undefined,
          name: undefined,
          path: undefined,
          icon: undefined,
          permission: undefined,
          menuType: '0',
          keepAlive: '0',
          sortOrder: 999,
          names: [
            { languageType: 'zh_cn', languageText: '' },
            { languageType: 'zh_hk', languageText: '' },
            { languageType: 'en_us', languageText: '' }
          ]
        },
        // 表单校验
        rules: {
          name: [
            {required: true, message: "菜单名称不能为空", trigger: "blur"}
          ],
          sortOrder: [
            {required: true, message: "菜单顺序不能为空", trigger: "blur"}
          ],
          path: [
            {required: true, message: "路由地址不能为空", trigger: "blur"}
          ],
          keepAlive: [
            {required: true, message: "路由缓冲不能为空", trigger: "blur"}
          ],
          permission: [
            {required: true, message: "权限标识不能为空", trigger: "blur"}
          ]
        }
      };
    },
    methods: {
      async handleNameInput() {
        // 输入中文后自动设置多语言
        const value = this.form.name;
        if (!value) return;
        
        // 直接设置简体中文和繁体中文（暂时相同）
        this.form.names = [
          { languageType: 'zh_cn', languageText: value },
          { languageType: 'zh_hk', languageText: value }, // 繁体中文暂与简体相同
          { languageType: 'en_us', languageText: value }  // 英文暂时与中文相同，后续可接入翻译API
        ];
        
        // 提示用户翻译功能暂不可用
        this.$message.info('自动翻译功能暂不可用，请手动编辑英文内容');
      },
      init(isEdit, id) {
        if (id != null) {
          this.form.parentId = id;
        }
        this.visible = true
        this.getTreeselect();
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (isEdit) {
            getObj(id).then(response => {
              this.form = response.data.data
            })
          } else {
            this.form.menuId = undefined
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            if (this.form.parentId === undefined) {
              this.form.parentId = -1
            }
            // 确保多语言数据完整
            if (!this.form.names[0].languageText) {
              this.form.names[0].languageText = this.form.name;
            }
            if (!this.form.names[1].languageText) {
              this.form.names[1].languageText = this.form.name;
            }
            if (!this.form.names[2].languageText) {
              this.form.names[2].languageText = this.form.name;
            }

            if (this.form.menuId) {
              putObj(this.form).then(data => {
                this.$message.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              });
            } else {
              addObj(this.form).then(data => {
                this.$message.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              })
            }
          }
        })
      },
      /** 查询菜单下拉树结构 */
      getTreeselect() {
        fetchMenuTree().then(response => {
          this.menuOptions = [];
          const menu = {id: -1, name: '根菜单', children: []};
          menu.children = response.data.data;
          this.menuOptions.push(menu);
        });
      },
      /** 转换菜单数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.id,
          label: node.name,
          children: node.children
        };
      }
    }
  };
</script>
