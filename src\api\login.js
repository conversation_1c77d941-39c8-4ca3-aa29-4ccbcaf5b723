
import request from '@/router/axios'
import qs from 'qs'
import store from '@/store'
import {getStore, setStore} from "@/util/store.js";
import website from "@/const/website.js";

const scope = 'server'

/**
 * 使用用户名密码和谷歌验证码登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {string} totpCode 谷歌验证码（6位数字）
 * @returns {Promise}
 */
export const loginByUsername = (username, password, totpCode) => {
  let grant_type = 'password'

  // 构建完整的请求参数对象
  const requestParams = {
    'username': username,
    'password': password,
    'grant_type': grant_type
  }

  // 添加谷歌验证码参数
  if (totpCode) {
    requestParams.totpcode = totpCode
  }

  // 序列化所有参数到请求体
  let dataObj = qs.stringify(requestParams)
  let basicAuth = 'Basic ' + window.btoa(website.formLoginClient)

  // 保存当前选中的 basic 认证信息
  setStore({
    name: 'basicAuth',
    content: basicAuth,
    type: 'session'
  })

  // 获取当前租户ID，优先使用存储的租户ID
  const tenantId = getStore({name: 'tenantId'}) || '1'

  return request({
    url: '/auth/oauth/token',
    headers: {
      isToken: false,
      'TENANT-ID': tenantId,
      'Authorization': basicAuth,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    method: 'post',
    data: dataObj
  })
}

export const refreshToken = (refresh_token) => {
  const grant_type = 'refresh_token'
  // 获取当前选中的 basic 认证信息
  let basicAuth = getStore({name: 'basicAuth'})

  // 获取当前租户ID
  const tenantId = getStore({name: 'tenantId'}) || '1'

  return request({
    url: '/auth/oauth/token',
    headers: {
      'isToken': false,
      'TENANT-ID': tenantId,
      'Authorization': basicAuth,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    method: 'post',
    params: {refresh_token, grant_type, scope}
  })
}

export const loginByMobile = (mobile, code) => {
  const grant_type = 'mobile'
  let basicAuth = 'Basic ' + window.btoa(website.smsLoginClient)

  // 保存当前选中的 basic 认证信息
  setStore({
    name: 'basicAuth',
    content: basicAuth,
    type: 'session'
  })

  // 获取当前租户ID
  const tenantId = getStore({name: 'tenantId'}) || '1'

  return request({
    url: '/auth/oauth/token',
    headers: {
      isToken: false,
      'TENANT-ID': tenantId,
      'Authorization': basicAuth,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    method: 'post',
    params: {mobile: 'SMS@' + mobile, code: code, grant_type}
  })
}

export const loginBySocial = (state, code) => {
  const grant_type = 'social'  // 修正grant_type为social
  let basicAuth = 'Basic ' + window.btoa(website.socialLoginClient)

  // 保存当前选中的 basic 认证信息
  setStore({
    name: 'basicAuth',
    content: basicAuth,
    type: 'session'
  })

  // 获取当前租户ID
  const tenantId = getStore({name: 'tenantId'}) || '1'

  return request({
    url: '/auth/oauth/token',
    headers: {
      isToken: false,
      'TENANT-ID': tenantId,
      'Authorization': basicAuth,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    method: 'post',
    params: {social: state + '@' + code, code: code, grant_type}  // 修正参数名为social
  })
}

export const getUserInfo = () => {
  return request({
    url: '/admin/user/info',
    method: 'get'
  })
}

export const logout = () => {
  return request({
    url: '/auth/token/logout',
    method: 'delete'
  })
}

/**
 * 校验令牌，若有效期小于半小时自动续期
 * @param refreshLock
 * @param $store
 */
export const checkToken = (refreshLock, $store) => {
  const token = store.getters.access_token
  if (!token) {
    console.warn('No access token found')
    return Promise.reject(new Error('No access token'))
  }

  // 获取当前选中的 basic 认证信息
  let basicAuth = getStore({name: 'basicAuth'})
  if (!basicAuth) {
    console.warn('No basic auth found')
    return Promise.reject(new Error('No basic auth'))
  }

  return request({
    url: '/auth/oauth/check_token',
    headers: {
      isToken: false,
      Authorization: basicAuth
    },
    method: 'get',
    params: {token}
  }).then(response => {
    const expire = response && response.data && response.data.exp
    if (expire) {
      const expiredPeriod = expire * 1000 - new Date().getTime()
      // 小于半小时自动续约
      if (expiredPeriod <= 30 * 60 * 1000) {
        if (!refreshLock) {
          refreshLock = true
          return $store.dispatch('RefreshToken')
            .then(() => {
              refreshLock = false
            })
            .catch((error) => {
              refreshLock = false
              console.error('Token refresh failed:', error)
              throw error
            })
        }
      }
    }
    return response
  }).catch(error => {
    console.error('Token check failed:', error)
    throw error
  })
}

/**
 * 注册用户
 */
export const registerUser = (userInfo) => {
  return request({
    url: '/admin/register/user',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: userInfo
  })
}

/**
 * 验证Token有效性（同步方法）
 * @param token
 */
export const validateToken = (token) => {
  if (!token) return false

  try {
    // 简单的Token格式验证
    const parts = token.split('.')
    if (parts.length !== 3) return false

    // 检查Token是否过期（如果是JWT格式）
    const payload = JSON.parse(atob(parts[1]))
    const currentTime = Math.floor(Date.now() / 1000)

    return payload.exp && payload.exp > currentTime
  } catch (error) {
    console.error('Token validation error:', error)
    return false
  }
}

/**
 * 清除所有认证信息
 */
export const clearAuthData = () => {
  setStore({
    name: 'access_token',
    content: '',
    type: 'session'
  })
  setStore({
    name: 'refresh_token',
    content: '',
    type: 'session'
  })
  setStore({
    name: 'basicAuth',
    content: '',
    type: 'session'
  })
  setStore({
    name: 'userInfo',
    content: {},
    type: 'session'
  })
}

/**
 * 获取当前登录状态
 */
export const getLoginStatus = () => {
  const token = store.getters.access_token
  const userInfo = store.getters.userInfo

  return {
    isLoggedIn: !!token && validateToken(token),
    token,
    userInfo,
    hasValidToken: validateToken(token)
  }
}
