/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

import {getObj} from "@/api/admin/tenant.js";
import {validatenull} from "@/util/validate.js";

const validateTenantCode = (rule, value, callback) => {
  let obj = {'code': value}
  getObj(obj).then(response => {
    if (window.boxType === 'edit') {
      return callback()
    }
    const result = response.data.data
    if (result && result.length !== 0) {
      callback(new Error('同名做市商标识已存在'))
    } else {
      callback()
    }
  })
}

const validateTenantDomain = (rule, value, callback) => {
  if (validatenull(value)) {
    return callback()
  }

  let obj = {'tenantDomain': value}
  getObj(obj).then(response => {
    if (window.boxType === 'edit') {
      return callback()
    }
    const result = response.data.data
    if (result && result.length !== 0) {
      callback(new Error('域名已存在'))
    } else {
      callback()
    }
  })
}

export const tableOption = {
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  searchMenuSpan: 4,
  align: 'center',
  column: [
    {
      label: '做市商id',
      prop: 'id',
      hide: true,
      editDisabled: true,
      editDisplay: false,
      addDisplay: false
    },
    {
      label: '做市商名称',
      prop: 'name',
      search: true,
      searchLabelWidth: 120,
      searchSpan: 5,
      rules: [
        {required: true, message: '请输入做市商名称', trigger: 'blur'},
        {min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur'}
      ]
    },
    {
      label: '做市商编号',
      type: 'number',
      prop: 'code',
      editDisabled: true,
      rules: [{
        required: true,
        message: '请输入做市商编号',
        trigger: 'blur'
      }, {validator: validateTenantCode, trigger: 'blur'}]
    },
    {
      label: '做市商域名',
      prop: 'tenantDomain',
       searchSpan: 5,
      searchLabelWidth: 120,
      search: true,
      rules: [{
        required: false,
        message: '请输入做市商域名',
        trigger: 'blur'
      }, {validator: validateTenantDomain, trigger: 'blur'}]
    },
     {
      label: '邀请码',
      searchSpan: 4,
      prop: 'invitationCode',
      search: true,
      addDisplay: false,
      editDisplay: false
    },
     {
      label: '风控总账(万)',
      prop: 'cash',
      search: false
    },
    {
      label: '开始时间',
      prop: 'startTime',
      type: 'datetime',
      format: 'yyyy-MM-dd hh:mm:ss',
      valueFormat: 'yyyy-MM-dd hh:mm:ss',
      rules: [{
        required: true,
        message: '请输入结束时间',
        trigger: 'blur'
      }]
    },
    {
      label: '结束时间',
      prop: 'endTime',
      type: 'datetime',
      format: 'yyyy-MM-dd hh:mm:ss',
      valueFormat: 'yyyy-MM-dd hh:mm:ss',
      rules: [{
        required: true,
        message: '请输入结束时间',
        trigger: 'blur'
      }]
    },
    {
      label: '状态',
      searchSpan: 4,
      prop: 'status',
      type: 'radio',
      border: true,
      dicUrl: '/admin/dict/type/status_type',
      search: true
    }
  ]
}
